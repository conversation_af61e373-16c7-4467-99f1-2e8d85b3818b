import { Message } from '@/store/chat'
import { memo, useMemo } from 'react'

export const MessageText = memo(({ message }: { message: Message }) => {
  // 将字符串中的 \n 转换为真实的换行符
  const data = useMemo(() => {
    let type = 'text'
    let content = message.content
    // 如果内容中包含 \n 字符序列，将其替换为真实的换行符
    if (content && content.includes('\\n')) {
      content = content.replace(/\\n/g, '\n')
    }
    if (content.includes('img:')) {
      type = 'image'
      // 'img:xxx,text:xxx'
      content = content.replace('img:', '')
    }
    const isHuman = message.type === 'human'
    const containsImage = /!\[.*?\]\(.*?\)/.test(message.content) // 检测 Markdown 是否包含图片
    return { processedContent: content, isHuman, containsImage, type }
  }, [message])

  return data.processedContent ? (
    <div className={`flex rounded-[20px] ${data.isHuman ? 'justify-end mx-[20px]' : 'justify-start'} mb-2 AA`}>
      {data.type === 'image' && (
        <div className="bg-[#EFEFF2] w-[500px] h-[500px] rounded-[16px]">
          <img className="w-[500px] h-[500px]" src={data.processedContent} alt="" />
        </div>
      )}
      {data.type === 'text' && (
        <div
          className={`px-[20px] py-[10px] text-[28px] rounded-lg w-auto ${
            data.isHuman ? 'bg-[#000000] text-white' : 'text-black'
          } ${data.isHuman && data.containsImage ? 'flex flex-col items-end' : ''}`} // 添加样式
        >
          {data.processedContent}
        </div>
      )}
    </div>
  ) : null
})
